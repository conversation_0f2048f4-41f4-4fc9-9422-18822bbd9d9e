import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import "./recording.scss";
import { IoMdInformationCircle } from "react-icons/io";
import { decoder, modalNotification, formatDate } from "../../../utils";
import { VideoConferenceService } from "../../../services";
import SubRecordings from "./SubRecordings";
import ShareModal from "./Components/ShareModal/ShareModal";
import TranscriptionHeader from "./Components/Header";

/**
 * @typedef {Object} Recording
 * @property {string} id - Recording ID
 * @property {string} title - Recording title
 * @property {string} recording_url - URL to the recording
 * @property {number} duration_minutes - Duration in minutes
 * @property {string} recording_size_formatted - Formatted size
 * @property {string} transacrption_status - Transcription status
 * @property {Object} Meeting - Meeting details
 * @property {Object} MeetingSessionLog - Session log details
 * @property {string} session_id - Session ID
 */

export default function Recordings() {
  const { meetingId } = useParams();
  // UI State
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showDelete, setShowDelete] = useState(false);
  const [showDownload, setShowDownload] = useState(false);

  // Data State
  const [decodedMeetingId, setDecodedMeetingId] = useState("");
  const [subRecordings, setSubRecordings] = useState([]);
  const [filteredRecordings, setFilteredRecordings] = useState([]);
  const [selectedRecording, setSelectedRecording] = useState(null);
  const [recordingAndAnalysisPageUrl, setRecordingAndAnalysisPageUrl] = useState(null);
  const [eventName, setEventName] = useState(null);
  const [emails, setEmails] = useState([]);


  // UseEffect to extract decoded meetingId from meetingId
  useEffect(() => {
    if (meetingId) {
      setDecodedMeetingId(decoder(meetingId));
    }
  }, [meetingId]);

  // Email validation handler
  const handleChange = (value) => {
    const validEmails = value.filter((email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email));
    if (value.length !== validEmails.length) {
      modalNotification({
        type: "error",
        message: "Invalid email address",
      });
    }
    setEmails(validEmails);
  };

  // Fetch recordings data
  const getRecordings = async () => {
    setIsLoading(true);
    try {
      const response = await VideoConferenceService.getRecordingLinksOfAMeetingService(
        decodedMeetingId
      );
      const { data, message, success } = response;

      if (success) {
        const updatedRecordings = data.map((item, index) => ({
          id: item.recording_data.id,
          title: `Recording ${index + 1}`,
          recording_url: item.recording_data.recording_url,
          duration_minutes: item.recording_data.duration_minutes,
          recording_size_formatted: `${(item.recording_data.size / 1024).toFixed(2)} KB`,
          Meeting: {
            id: item.meeting_id,
            start_date: item.recording_data.start_time,
          },
          MeetingSessionLog: {
            id: item.session_id,
            started_at: item.recording_data.start_time,
          },
          transcription_data: item.transcription_data,
        }));

        setSubRecordings(updatedRecordings);
        setFilteredRecordings(updatedRecordings);
        if (data.length > 0) {
          setEventName(data[0].event_name);
        }
      } else {
        console.error("Error fetching recordings:", message);
        modalNotification({
          type: "error",
          message: "Something went wrong, Please try again later",
        });
      }
    } catch (error) {
      console.error("Error in getRecordings:", error);
      modalNotification({
        type: "error",
        message: "Something went wrong, Please try again later",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Modal handlers
  const handleOk = () => setIsModalOpen(false);
  const handleCancel = () => setIsModalOpen(false);

  // Search handler
  const onSearch = (value) => {
    if (!value.trim()) return;
    const filteredData = subRecordings.filter((item) =>
      item.title.toLowerCase().includes(value.toLowerCase())
    );
    setFilteredRecordings(filteredData);
  };

  // Delete handler
  const handleDelete = async () => {
    try {
      const deletePromises = selectedRecording.map(recording =>
        VideoConferenceService.deleteSubRecordingService(recording.id)
      );

      const responses = await Promise.all(deletePromises);
      const allSuccessful = responses.every(response => response.success);

      if (allSuccessful) {
        getRecordings();
        setShowDelete(false);
        setShowDownload(false);
        setSelectedRecording(null);
      } else {
        console.error("Error deleting recordings");
      }
    } catch (error) {
      console.error("Error in handleDelete:", error);
    }
  };

  // Download handler
  const handleDownload = () => {
    if (!selectedRecording?.length) {
      console.error("No recordings selected for download");
      return;
    }

    selectedRecording.forEach((item) => {
      const url = item.recording_url;
      const a = document.createElement("a");
      a.href = url;
      a.download = item.title;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    });

    setShowDownload(false);
    setShowDelete(false);
    setSelectedRecording(null);
  };


  //  useEffects to fetch recordings
  useEffect(() => {
    if (decodedMeetingId) {
      getRecordings();
    }
  }, [decodedMeetingId]);

  return (
    <div className="recordings-container">
      <TranscriptionHeader
        filteredRecordings={filteredRecordings}
        getRecordings={getRecordings}
        isLoading={isLoading}
        onSearch={onSearch}
        showDelete={showDelete}
        setShowDelete={setShowDelete}
        handleDelete={handleDelete}
        showDownload={showDownload}
        handleDownload={handleDownload}
        eventName={eventName}
      />

      <div className="recordings-header-info">
        <IoMdInformationCircle className="recordings-header-info-icon" />
        <p>
          {subRecordings?.length === 0
            ? "This meeting has no recording."
            : subRecordings?.length === 1
            ? "This meeting has single recording."
            : "This meeting has multiple recordings."}
        </p>
      </div>

      <SubRecordings
        filteredRecordings={filteredRecordings}
        setSelectedRecording={setSelectedRecording}
        setRecordingAndAnalysisPageUrl={setRecordingAndAnalysisPageUrl}
        formatDate={formatDate}
        setIsModalOpen={setIsModalOpen}
        setShowDelete={setShowDelete}
        setShowDownload={setShowDownload}
      />

      <ShareModal
        isModalOpen={isModalOpen}
        handleOk={handleOk}
        handleCancel={handleCancel}
        setIsModalOpen={setIsModalOpen}
        selectedRecording={selectedRecording}
        emails={emails}
        handleChange={handleChange}
        recordingAndAnalysisPageUrl={recordingAndAnalysisPageUrl}
      />
    </div>
  );
}
