import React from "react";
import { <PERSON>, Container, Row } from "react-bootstrap";
import { Helmet } from "react-helmet-async";
import { <PERSON> } from "react-router-dom";
import { ImageElement } from "../../../components";
import "./HowItWorks.scss";

export default function HowItWorks() {
  return (
    <>
      <Helmet>
        <title>
          How Daakia Works: Best Video Conferencing & Secure Messaging
        </title>
        <meta
          name="description"
          content="Learn how Daakia ensures secure communication through end-to-end encryption, private video conferencing, and cross-platform messaging for businesses and professionals. "
        />
        {/* <meta name="keywords" content="React, Helmet, SEO, Meta Tags" /> */}
      </Helmet>
      <section id="howItWorks" className="pt-lg-0 py-70 howWorks bg-white">
        <Container>
          <div className="text-center heading mx-auto">
            <h2 className="heading_sub font-ad">How It Works</h2>
            <h1 className="heading_main">How to Make Best Use of Daakia</h1>
            <p className="mb-0">
              A cross-platform web & mobile solution to make collaboration and
              communication easy and effective; be it at office, home or on the
              go. Bring your work, classroom or webinar together on Daakia and
              enjoy a more streamlined, secured experience that makes seamless
              cross-language communication possible.
            </p>
          </div>
          <div className="howItWorks-secure">
            <h1>Secure Video Conferencing & Messaging</h1>
            <ul>
              <li>
                Daakia is a secure digital collaboration platform featuring
                encrypted chat.
              </li>
              <li>
                It offers video conferencing software and online meeting
                platforms.
              </li>
              <li>
                <div>
                  Designed for businesses, professionals, and remote teams to
                  ensure seamless and secure communication.
                </div>
              </li>
            </ul>
          </div>
          <div
            className="cta-box"
            style={{
              // backgroundColor: '#F4F8F9',
              padding: "20px",
              // borderRadius: '10px',
              // boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
              // border: '1px solid #ddd',
              marginTop: "20px",
              marginBottom: "20px",
              textAlign: "center",
            }}
          >
            <h2
              style={{
                color: "#25E89F",
                fontFamily: "ArchitectsDaughter-Regular",
              }}
            >
              Start Your Secure Communication Journey Today!
            </h2>
            <p
              style={{ color: "#333", fontFamily: "Inter", marginTop: "10px" }}
            >
              <Link to="/signup">Sign Up</Link> for Daakia now and experience seamless, secure
              collaboration.
            </p>
          </div>
          <div className="howWorks_cnt">
            <Row className="gx-2 gx-sm-3 justify-content-center">
              <Col>
                <div
                  className="box text-center"
                  style={{
                    backgroundImage:
                      "url('./assets/images/frontend/how-works/box-1-bg.svg')",
                  }}
                >
                  <ImageElement
                    source="how-works/create-account.svg"
                    alt="create-and-verify-account"
                  />
                  <p className="mb-0 font-bd">
                    Create & <br />
                    Verify Account
                  </p>
                </div>
              </Col>
              <Col>
                <div
                  className="box text-center"
                  style={{
                    backgroundImage:
                      "url('./assets/images/frontend/how-works/box-2-bg.svg')",
                  }}
                >
                  <ImageElement
                    source="how-works/language.svg"
                    alt="bring-the-world-closer-together"
                  />
                  <p className="mb-0 font-bd">
                    Bring The World <br /> Closer Together
                  </p>
                </div>
              </Col>
              <Col>
                <div
                  className="box text-center"
                  style={{
                    backgroundImage:
                      "url('./assets/images/frontend/how-works/box-3-bg.svg')",
                  }}
                >
                  <ImageElement
                    source="how-works/speak.svg"
                    alt="speak-freely-with-your-loved-ones"
                  />
                  <p className="mb-0 font-bd">
                    Speak Freely With <br /> Your Loved Ones
                  </p>
                </div>
              </Col>
              <Col>
                <div
                  className="box text-center"
                  style={{
                    backgroundImage:
                      "url('./assets/images/frontend/how-works/box-4-bg.svg')",
                  }}
                >
                  <ImageElement
                    source="how-works/transactions.svg"
                    alt="simple-and-fast-transactions"
                  />
                  <p className="mb-0 font-bd">
                    Simple & Fast <br />
                    Transactions
                  </p>
                </div>
              </Col>
              <Col>
                <div
                  className="box text-center"
                  style={{
                    backgroundImage:
                      "url('./assets/images/frontend/how-works/box-5-bg.svg')",
                  }}
                >
                  <ImageElement
                    source="how-works/events.svg"
                    alt="create-and-organize-events"
                  />
                  <p className="mb-0 font-bd">Create & Organize Events</p>
                </div>
              </Col>
              <Col>
                <div
                  className="box text-center"
                  style={{
                    backgroundImage:
                      "url('./assets/images/frontend/how-works/box-6-bg.svg')",
                  }}
                >
                  <ImageElement
                    source="how-works/status.svg"
                    alt="post-status-and-share-your-memories"
                  />
                  <p className="mb-0 font-bd">
                    Post Status & <br /> Share Your Memories
                  </p>
                </div>
              </Col>
              <Col>
                <div
                  className="box text-center"
                  style={{
                    backgroundImage:
                      "url('./assets/images/frontend/how-works/box-7-bg.svg')",
                  }}
                >
                  <ImageElement
                    source="how-works/clips.svg"
                    alt="express-yourself-with-clips"
                  />
                  <p className="mb-0 font-bd">
                    Express Yourself
                    <br /> With Clips
                  </p>
                </div>
              </Col>
            </Row>
          </div>
        </Container>
      </section>
    </>
  );
}
