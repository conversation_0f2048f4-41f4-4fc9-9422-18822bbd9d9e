import React from 'react';

/**
 * TestButton Component
 * Reusable test button for microphone and speaker testing
 * @param {function} onClick - Click handler function
 * @param {boolean} isActive - Whether the test is currently active
 * @param {string} activeText - Text to show when active
 * @param {string} inactiveText - Text to show when inactive
 * @param {boolean} disabled - Whether the button is disabled
 */
function TestButton({ 
  onClick, 
  isActive = false, 
  activeText = 'Stop Test', 
  inactiveText = 'Test', 
  disabled = false 
}) {
  return (
    <div
      onClick={disabled ? undefined : onClick}
      style={{
        height: '40px',
        padding: '0 16px',
        border: 'none',
        borderRadius: '6px',
        background: isActive ? '#3B60E4' : '#D2D2D2',
        color: isActive ? '#fff' : '#555454',
        fontSize: '14px',
        fontWeight: '500',
        minWidth: '120px',
        cursor: disabled ? 'not-allowed' : 'pointer',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        opacity: disabled ? 0.5 : 1,
        userSelect: 'none'
      }}
    >
      {isActive ? activeText : inactiveText}
    </div>
  );
}

export default TestButton;
