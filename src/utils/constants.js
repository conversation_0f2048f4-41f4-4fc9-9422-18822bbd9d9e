export const OS_PLATFORM = Object.freeze({
  WINDOWS: "Windows",
  MAC: "Mac",
  LINUX: "Linux",
  UNKNOWN: "Unknown",
});

export const DESKTOP_DOWNLOAD_LINK = Object.freeze({
  WINDOWS: process.env.REACT_APP_DESKTOP_APP_DOWNLOAD_LINK_WINDOWS,
  MAC: process.env.REACT_APP_DESKTOP_APP_DOWNLOAD_LINK_MAC,
});


export const ANALYSIS_STATUS = Object.freeze({
  QUEUED: 'queued',
  TRANSCRIPTION_INPROGRESS: 'transcription_inprogress',
  TRANSCRIPTION_FAILED: 'transcription_failed',
  UNAV<PERSON><PERSON><PERSON><PERSON>: 'unavailable',
  ANALYSIS_INPROGRESS: 'analysis_inprogress',
  ANALYSIS_FAILED: 'analysis_failed',
  COMPLETED: 'completed'
});