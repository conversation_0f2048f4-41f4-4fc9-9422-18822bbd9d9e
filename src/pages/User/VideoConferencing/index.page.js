import { t } from "i18next";

import React, { useState, useEffect, useCallback } from "react";
import "./RecordingList.scss";
import { FaSortDown } from "react-icons/fa";
import {
  MdKeyboardArrowRight,
  MdKeyboardArrowLeft,
  MdOutlineFileDownload,
} from "react-icons/md";
import { RiDeleteBin6Line } from "react-icons/ri";
import { IoMdEye } from "react-icons/io";
// import { BsThreeDotsVertical } from "react-icons/bs";
import { Button, Checkbox, DatePicker, Dropdown, Input } from "antd";
import {
  ReloadOutlined,
  // InfoCircleFilled
} from "@ant-design/icons";
import { Container, Table, Col, Row } from "react-bootstrap";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import isElectron from "is-electron";
import moment from "moment";
import axios from "axios";
import { ReactComponent as RecordedMeeting } from "./Assets/RecordedVideo.svg";

import {
  ImageElement,
  ModalComponent,
  RippleEffect,
  PlanMeetingForm,
  Tabs,
  // nameFormatter,
  textFormatter,
  GlobalLoader,
  checkValidData,
  SweetAlert,
  Pagination,
  StartMeetingsForm,
  ShareForm,
  Loader,
  // RecordingForm,
  // CommonButton,
} from "../../../components";
// import { RecordingList } from "./RecordingList";
import {
  selectProfileData,
  selectSubscriptionData,
  selectUserAccountData,
} from "../../../redux/UserSlice/index.slice";
import userRoutesMap from "../../../routeControl/userRoutes";
// import userRoutesMap from "../../../routeControl/userRoutes";
import { CommonServices, VideoConferenceService } from "../../../services";
import {
  baseUrlGenerator,
  dateFormatter,
  encoder,
  getActiveAccount,
  getCurrentActiveSubscription,
  getIscorporateActive,
  getTimeZoneData,
  loadMeetingInDesktopApp,
  logger,
  modalNotification,
  momentDateTimeTimezoneFormatter,
  momentTimezoneFormatter,
  validateMeetingUrl,
} from "../../../utils";
import { timezoneData } from "../../../config/timezoneData";
import { constants } from "../DaakiaVC/utils/constants";
import JoinMeetingForm from "../../../components/Form/User/JoinMeetingForm/index.form";
import RecordingLists from "./RecordingLists";

function VideoConferencing() {
  const [planMeeting, setPlanMeeting] = useState(false);
  const [isAlertVisible, setIsAlertVisible] = useState(false);
  const [isAlertRecordingVisible, setIsAlertRecordingVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [recordingLoading, setRecordingLoading] = useState(false);
  const [startMeetingloading, setStartMeetingLoading] = useState(false);
  const [joinMeetingloading, setJoinMeetingLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(true);
  const [defaultKey, setDefaultKey] = useState("upcoming");
  const [tableData, setTableData] = useState([]);
  const userData = useSelector(selectProfileData);
  const [planData, setPlanData] = useState({});
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteId, setDeleteId] = useState("");
  const [noOfPage, setNoOfPage] = useState(0);
  const [count, setTotalCount] = useState(2);
  const [shareId, setShareId] = useState("");
  const [shareMeetingInfo, setShareMeetingInfo] = useState({});
  const sizePerPage = 10;
  const [page, setPage] = useState(1);
  const [firstTimeFetch, setFirstTimeFetch] = useState(false);
  const userAccounts = useSelector(selectUserAccountData);
  const userActiveSubscriptions = useSelector(selectSubscriptionData);
  // const [enableConfrencing,setEnableConfrencing] = useState(false);
  const [isGuest, setIsGuest] = useState(false);

  // const [recurringMeeting, setRecurringMeeting] = useState(false);
  const handleClose = () => {
    setPlanMeeting(false);
    setPlanData({});
  };

  const [recordingModal, setRecordingModal] = useState(false);
  const [shareModal, setShareModal] = useState(false);
  const [startMeetingModal, setStartMeetingModal] = useState(false);
  const [meetingId, setMeetingId] = useState("");
  const [allRecordingUrl, setAllRecordingUrl] = useState([]);
  const [isRecordingLoading, setIsRecordingLoading] = useState(false);
  const [joinMeetingModal, setJoinMeetingModal] = useState(false);
  const [loadingRows, setLoadingRows] = useState({});

  const [sipData, setSipData] = React.useState({});

  const [dataDuration, setDataDuration] = useState("All");
  const { Search } = Input;
  const { RangePicker } = DatePicker;

  // Meeting Recording
  const [selectAllRecordings, setSelectAllRecordings] = useState(false);
  const [recordingData, setRecordingData] = useState(tableData);
  const [selectedItems, setSelectedItems] = useState([]);
  const [showDeleteRecordingModal, setShowDeleteRecordingModal] =
    useState(false);
  const [recordingIds, setRecordingIds] = useState([]);
  const [searchValue, setSearchValue] = useState("");

  const [dateRangeValue, setDateRangeValue] = useState(null);

  const downloadAttendanceReport = (data, idMeeting) => {
    const attendanceReport = {
      main: {
        title: "Attendance Report\n\n",
        data: [
          `Meeting Title, ${textFormatter(data?.event_name)}`,
          `Attended Participants, ${data?.MeetingLog?.peak_participants}`,
          `Start Time, ${moment(data?.start_date).format(
            "DD MMM YYYY, hh:mm A"
          )}`,
          `End Time, ${moment(data?.end_date).format("DD MMM YYYY, hh:mm A")}`,
          `Meeting Duration, ${
            data?.MeetingLog?.duration_minutes >= 60
              ? `${Math.floor(data?.MeetingLog?.duration_minute / 60)} Hour ${
                  data?.MeetingLog?.duration_minutes % 60
                } minutes`
              : `${data?.MeetingLog?.duration_minute} Minutes`
          }`,
          `Average Attendance Time, ${
            data?.MeetingLog?.duration_minute /
            data?.MeetingLog?.peak_participants
          } Minutes`,
        ],
      },
    };

    // Create CSV content
    let csvContent =
      attendanceReport.main.title + attendanceReport.main.data.join("\n");
    csvContent += "\n\nIn Meeting Activities";
    // Add session data for each session
    if (data?.MeetingSessionLogs && data.MeetingSessionLogs.length > 0) {
      data.MeetingSessionLogs.forEach((session, index) => {
        // Add a separator between sessions
        csvContent += `\n\nSession ${index + 1} (ID: ${session.id})\n`;
        csvContent += "Name,Join Time,Leave Time,Duration,Email,Role\n";

        // Add participant data for this session
        if (
          session.MeetingAttendances &&
          session.MeetingAttendances.length > 0
        ) {
          session.MeetingAttendances.forEach((participant) => {
            csvContent += `${participant.screen_name || "-"},`;
            csvContent += `${
              moment(participant.joined_at).format("HH:mm A") || "-"
            },`;
            csvContent += `${
              moment(participant.leave_at).format("HH:mm A") || "-"
            },`;
            csvContent += `${participant.duration_formatted || "-"},`;
            csvContent += `${participant.email || "-"},`;
            csvContent += `${participant.role || "-"}\n`;
          });
        } else {
          csvContent += "No participants data available\n";
        }
      });
    } else {
      csvContent += "\n\nNo session data available";
    }

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `attendance_report_${data?.event_name || "meeting"}.csv`;
    a.click();
    URL.revokeObjectURL(url);
    setLoadingRows((prev) => ({ ...prev, [idMeeting]: false }));
  };

  const getAttendanceReport = async (idMeeting) => {
    setLoadingRows((prev) => ({ ...prev, [idMeeting]: true }));
    const res = await VideoConferenceService.getAttendanceReportService(
      idMeeting
    );
    const { success, message, data } = res;
    if (success === 1) {
      downloadAttendanceReport(data, idMeeting);
    } else {
      modalNotification({
        type: "error",
        message,
      });
    }
    setLoadingRows((prev) => ({ ...prev, [idMeeting]: false }));
  };

  // const triggerTranscriptionAnalysis = async (idMeeting) => {
  //   if (!idMeeting || idMeeting.length === 0) {
  //     return; // Don't make the API call if there are no meetings
  //   }
  //   try {
  //     const res = await VideoConferenceService.startTranscriptionAnalysisService(
  //       idMeeting
  //     );
  //     const { success, message } = res;
  //     if (success === 1) {
  //       console.log("success", message);
  //     } else {
  //       console.log("error", message);
  //     }
  //   } catch (error) {
  //     // Don't show error toast for empty meetings case
  //     logger(error);
  //   }
  // };

  // useEffect(() => {
  //   if (meetingIdArray.length > 0 && defaultKey === "recorded") {
  //     triggerTranscriptionAnalysis(meetingIdArray);
  //   }
  // }, [defaultKey]);

  const handleShareModalShow = () => {
    setShareModal(true);
    // getMeetingSipData(shareId);
  };

  const handleRecordingModalShow = async (id) => {
    setIsRecordingLoading(true);
    setRecordingModal(true);
    try {
      const response = await VideoConferenceService.getRecordingLinksService(
        id
      );
      const { success, message, data } = response;
      if (success === 1) {
        setAllRecordingUrl(data);
        setMeetingId(id);
      } else {
        modalNotification({
          type: "error",
          message,
        });
      }
    } catch (error) {
      logger(error);
    }
    setIsRecordingLoading(false);
  };

  const handleShareModalClose = () => {
    setShareId("");
    setShareMeetingInfo({});
    setShareModal(false);
  };
  const videoSubscription = getCurrentActiveSubscription(
    userActiveSubscriptions,
    "videoConferencing",
    true
  );
  const handleRecordingModalClose = () => {
    setRecordingModal(false);
  };
  const handleStartMeetingModalShow = () => {
    // add 5 min validation
    // let newStart = new Date(
    //   tableData?.[0]?.MeetingTimes?.[0]?.start_date
    // ).getTime();
    // let now = new Date().getTime();
    // let distance = newStart - now;
    // // end

    // if (distance < 1200000 && distance > 0) {
    //   modalNotification({
    //     type: "error",
    //     message:
    //       "Meetings can be initiated up to 20 minutes prior to the another scheduled meeting.",
    //   });
    // } else {
    //   setStartMeetingModal(true);
    // }
    setStartMeetingModal(true);
  };
  const validateStartMeeting = (event, item) => {
    // Validation to let user start meeting only before 20 mints
    const startTime = moment.utc(item?.start_date).valueOf();
    const now = moment.utc().valueOf();
    let distance = startTime - now;

    if (distance > 1200000) {
      event.preventDefault(); // Prevent the default action
      // to change the format of time to 24 hr, just replace the hh:mm A with HH:MM
      modalNotification({
        type: "error",
        message: `This meeting is scheduled for ${moment(startTime).format(
          "DD MMM YYYY, hh:mm A"
        )}. You can start the meeting 20 mins before scheduled time.`,
      });
      return false;
    }
    return true;
  };
  const handleStartMeetingModalClose = () => setStartMeetingModal(false);

  const handlePlanMeetingShow = () => setPlanMeeting(true);

  const goToPage = (pageNo) => {
    setPage(pageNo);
    window.scrollTo(0, 0);
  };
  const getMeetingList = async (
    startDate = "",
    endDate = "",
    searchTerm = ""
  ) => {
    setTableLoading(true);
    try {
      if (defaultKey === "recorded") {
        const params = {
          offset: (page - 1) * sizePerPage,
          limit: sizePerPage,
        };
        if(startDate && endDate) {
          params.start_date = startDate;
          params.end_date = endDate;
        }
        if(searchTerm) {
          params.keyword = searchTerm;
        }
        const res = await VideoConferenceService.getUserRecordingLinksService(
          params
        );
        const { success, data } = res;
        if (success === 1) {
          setTableData(data?.rows);
          setRecordingData(data?.rows);
          setNoOfPage(
            data?.count > 0 ? Math.ceil(data?.count / sizePerPage) : 1
          );
          setTotalCount(data?.count);
          // const meetingIdArray = data?.rows?.map((item) => String(item?.id));
          // if (
          //   userActiveSubscriptions[1]?.Subscription?.SubscriptionFeature
          //     ?.meeting_analysis === 1 &&
          //   meetingIdArray.length > 0
          // ) {
          //   await triggerTranscriptionAnalysis(meetingIdArray);
          // }
        }
      } else {
        let activeAccount = getActiveAccount(userAccounts);
        let queryParams = {
          offset: (page - 1) * sizePerPage,
          limit: sizePerPage,
          type: defaultKey,
          business_account_id: activeAccount?.id,
        };
        const res = await VideoConferenceService.meetingListService({
          queryParams,
        });
        const { success, message, data } = res;
        if (success === 1) {
          setTableData(data?.rows);
          setRecordingData(data?.rows);
          setNoOfPage(
            data?.count > 0 ? Math.ceil(data?.count / sizePerPage) : 1
          );
          setTotalCount(data?.count);
        } else {
          modalNotification({
            type: "error",
            message,
          });
        }
      }
    } catch (error) {
      logger(error);
    }
    setTableLoading(false);
  };

  useEffect(() => {
    setFirstTimeFetch(true);
    const activeAcc = getActiveAccount(userAccounts);
    setIsGuest(activeAcc?.account?.includes("(Guest)"));
  }, []);

  useEffect(() => {
    getMeetingList();
  }, [defaultKey]);

  useEffect(() => {
    if (firstTimeFetch) {
      getMeetingList();
    }
  }, [page]);

  const getRegion = useCallback(async () => {
    const maxAttempts = 3;
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        // eslint-disable-next-line no-await-in-loop
        const response = await axios.get(constants.REGION_LOCATOR);
        return response;
      } catch (error) {
        console.error(`Error getting region (attempt ${attempts + 1}):`, error);
        attempts += 1;
        if (attempts >= maxAttempts) {
          return { data: { preferred_video_server_id: "ap1" } };
        }
      }
    }
  }, []);

  const onSubmit = async (value) => {
    setLoading(true);
    const region = await getRegion();
    try {
      let bodyData = { ...value };
      delete bodyData.byAfter;
      let res;
      let date = "";
      let endDate = "";
      let timeZone = getTimeZoneData(timezoneData, value?.time_zone);
      const tzIdentifier = timeZone?.utc?.[0];

      if (planData?.id) {
        bodyData.start_date = momentDateTimeTimezoneFormatter(
          value?.start_date,
          timeZone?.utc?.[0],
          "YYYY-MM-DD"
        );

        bodyData.start_time =
          momentDateTimeTimezoneFormatter(value?.start_time, timeZone?.utc?.[0])
            .utc()
            .format() ===
          momentDateTimeTimezoneFormatter(
            planData?.start_date,
            timeZone?.utc?.[0]
          )
            .utc()
            .format()
            ? momentDateTimeTimezoneFormatter(
                value?.start_time,
                timeZone?.utc?.[0],
                "HH:mm"
              )
            : value?.start_time;
        date = `${bodyData.start_date} ${bodyData.start_time}`;

        bodyData.start_date = momentDateTimeTimezoneFormatter(
          date,
          timeZone?.utc?.[0]
        )
          .utc()
          .format();

        if (value?.is_recurring && !value?.end_occurrence) {
          bodyData.end_date = momentDateTimeTimezoneFormatter(
            value?.end_date,
            timeZone?.utc?.[0],
            "YYYY-MM-DD"
          );
          endDate = `${bodyData.end_date} ${bodyData.start_time}`;
          bodyData.end_date = momentDateTimeTimezoneFormatter(
            endDate,
            timeZone?.utc?.[0]
          )
            .utc()
            .format();
        }
        bodyData.seleted_time = date;
        bodyData.timezone_identifier = tzIdentifier;
        delete bodyData.start_time;
        bodyData.isType = true;
        res = await VideoConferenceService.updatePlanMeetingsService(
          planData?.id,
          bodyData
        );
      } else {
        date = `${value?.start_date} ${value?.start_time}`;

        bodyData.start_date = momentDateTimeTimezoneFormatter(
          date,
          timeZone?.utc?.[0]
        )
          .utc()
          .format();
        if (value?.is_recurring && !value?.end_occurrence) {
          endDate = `${bodyData.end_date} ${bodyData.start_time}`;
          bodyData.end_date = momentDateTimeTimezoneFormatter(
            endDate,
            timeZone?.utc?.[0]
          )
            .utc()
            .format();
        }

        bodyData.seleted_time = date;
        delete bodyData.start_time;
        let activeAccount = getActiveAccount(userAccounts);
        bodyData.is_corporate = getIscorporateActive(userAccounts);
        bodyData.corporate_id = activeAccount?.id;
        bodyData.timezone_identifier = tzIdentifier;
        bodyData.preferred_video_server_id =
          region?.data?.preferred_video_server_id;
        res = await VideoConferenceService.addMeetingService(bodyData);
      }
      const { success, message } = res;
      if (success === 1) {
        modalNotification({
          type: "success",
          message,
        });
        setPlanMeeting(false);
        getMeetingList();
        setPlanData({});
      } else {
        modalNotification({
          type: "error",
          message,
        });
      }
    } catch (error) {
      logger(error);
    }

    setLoading(false);
  };

  const getMeetingDetail = (data) => {
    let obj = {};
    obj.invitation = [];
    data?.MeetingInvites.map((item) => {
      obj.invitation.push(item.attendee);
    });
    let timeZone = getTimeZoneData(timezoneData, data?.time_zone);
    data.start_date = momentDateTimeTimezoneFormatter(
      data?.start_date,
      timeZone?.utc?.[0]
    );
    if (data?.is_recurring && !data?.end_occurrence)
      data.end_date = momentDateTimeTimezoneFormatter(
        data?.end_date,
        timeZone?.utc?.[0]
      );
    data.start_time = momentDateTimeTimezoneFormatter(
      data?.start_date,
      timeZone?.utc?.[0]
    );
    obj = { ...obj, ...data };
    setPlanData(obj);
  };

  const deletePlan = async () => {
    setDeleteLoading(true);
    try {
      let bodyData = { status: "deleted" };
      const res = await VideoConferenceService.updatePlanMeetingsService(
        deleteId,
        bodyData
      );
      const { success, message } = res;
      if (success === 1) {
        modalNotification({
          type: "success",
          message,
        });
        setDeleteId("");
        getMeetingList();
      } else {
        modalNotification({
          type: "error",
          message,
        });
      }
    } catch (error) {
      logger(error);
    }
    setDeleteLoading(false);
  };

  const foundMultipleRecordings = async () => {
    const meetingIds = selectedItems.map((item) => item?.id);
    setRecordingIds(meetingIds);
  };

  const deleteRecording = async () => {
    setRecordingLoading(true);
    try {
      if (recordingIds.length > 1) {
        // Handle multiple recordings
        recordingIds.map(async (item) => {
          const res = await VideoConferenceService.deleteRecordingService(
            item?.id
          );
          const { success, message } = res;
          if (success !== 1) {
            modalNotification({
              type: "error",
              message,
            });
          }
        });
        modalNotification({
          type: "success",
          message: "Recording(s) deleted successfully",
        });
        getMeetingList();
      } else {
        // Handle single recording
        const res = await VideoConferenceService.deleteRecordingService(
          recordingIds?.[0]?.id
        );
        const { success, message } = res;
        if (success !== 1) {
          modalNotification({
            type: "error",
            message,
          });
        }
      }

      modalNotification({
        type: "success",
        message: "Recording(s) deleted successfully",
      });
      getMeetingList();
    } catch (error) {
      logger(error);
      modalNotification({
        type: "error",
        message: "Failed to delete recording(s)",
      });
    }
    setRecordingLoading(false);
    setSelectedItems([]);
  };

  const onConfirmAlert = () => {
    deletePlan();
    setIsAlertVisible(false);
    return true;
  };

  const onConfirmRecordingAlert = () => {
    deleteRecording();
    setIsAlertRecordingVisible(false);
    return true;
  };

  const downloadRecording = async () => {
    try {
      if (selectedItems.length > 1) {
        selectedItems.map(async (item) => {
          const res = await VideoConferenceService.getRecordingLinksService(
            item?.id
          );
          const { success, message, data } = res;
          if (success === 1) {
            if (data.length > 1) {
              data.forEach((record, index) => {
                setTimeout(() => {
                  const link = document.createElement("a");
                  link.href = record?.recording_url;
                  link.setAttribute("download", "");
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }, index * 1000); // 1000ms (1s) delay between downloads
              });
            } else if (data.length === 1) {
              const link = document.createElement("a");
              link.href = data?.[0]?.recording_url;
              link.setAttribute("download", "");
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            } else {
              modalNotification({
                type: "error",
                message: "No recording found",
              });
            }
          } else {
            modalNotification({
              type: "error",
              message,
            });
          }
        });
      } else {
        const res = await VideoConferenceService.getRecordingLinksService(
          selectedItems?.[0]?.id
        );
        const { success, message, data } = res;
        if (success === 1) {
          if (data.length > 1) {
            data.forEach((item, index) => {
              setTimeout(() => {
                const link = document.createElement("a");
                link.href = item?.recording_url;
                link.setAttribute("download", "");
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }, index * 1000); // 1000ms (1s) delay between downloads
            });
          } else if (data.length === 1) {
            const link = document.createElement("a");
            link.href = data?.[0]?.recording_url;
            link.setAttribute("download", "");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          } else {
            modalNotification({
              type: "error",
              message: "No recording found",
            });
          }
        } else {
          modalNotification({
            type: "error",
            message,
          });
        }
      }
      // Reset the selected items and checkbox state after download
      setSelectedItems([]);
      setSelectAllRecordings(false);
    } catch (error) {
      logger(error);
    }
  };

  const getMeetingSipData = async (shareID) => {
    try {
      let res = await CommonServices.sipDetails(shareID);
      const { success, message } = res;
      if (success === 1) {
        setSipData(res.data);
      } else {
        modalNotification({
          type: "error",
          message,
        });
      }
    } catch (error) {
      logger(error);
    }
  };

  const handleDateRangeChange = (dates) => {
    if (!dates) {
      setDataDuration("All");
      setDateRangeValue(null);
      getMeetingList();
      return;
    }

    const startDate = moment(dates[0]).startOf('day');
    const endDate = moment(dates[1]).endOf('day');
    const today = moment().endOf('day');
    
    // Calculate the difference in days including both start and end dates
    const daysDifference = endDate.diff(startDate, 'days');
    
    // Only set to "Last 7 days" or "Last 30 days" if end date is today
    if (endDate.isSame(today, 'day')) {
      if (daysDifference === 7) {
        setDataDuration("Last 7 days");
      } else if (daysDifference === 30) {
        setDataDuration("Last 30 days");
      } else {
        setDataDuration(`Custom`);
      }
    } else {
      setDataDuration(`Custom`);
    }

    setDateRangeValue(dates);
    const startDates = startDate.format("YYYY-MM-DD");
    const endDates = endDate.format("YYYY-MM-DD");
    getMeetingList(startDates, endDates);
  };

  const dropdownItems = [
    {
      label: (
        <label
          onClick={() => {
            setDataDuration("All");
            setDateRangeValue(null);
            getMeetingList();
          }}
        >
          All
        </label>
      ),
      value: "all",
      key: 0,
    },
    {
      label: (
        <label
          onClick={() => {
            setDataDuration("Last 7 days");
            const today = moment().endOf('day');
            const sevenDaysAgo = moment().subtract(6, 'days').startOf('day');
            setDateRangeValue([sevenDaysAgo, today]);
            getMeetingList(sevenDaysAgo.format("YYYY-MM-DD"), today.format("YYYY-MM-DD"));
          }}
        >
          Last 7 days
        </label>
      ),
      value: "last7days",
      key: 1,
    },
    {
      label: (
        <label
          onClick={() => {
            setDataDuration("Last 30 days");
            const today = moment().endOf('day');
            const thirtyDaysAgo = moment().subtract(29, 'days').startOf('day');
            setDateRangeValue([thirtyDaysAgo, today]);
            getMeetingList(thirtyDaysAgo.format("YYYY-MM-DD"), today.format("YYYY-MM-DD"));
          }}
        >
          Last 30 days
        </label>
      ),
      value: "last30days",
      key: 2,
    },
  ];

  useEffect(() => {
    const todaysDate = moment();
    if (dataDuration === "Last 7 days") {
      const startDates = moment(todaysDate)
        .subtract(7, "days")
        .format("YYYY-MM-DD");
      const endDates = moment(todaysDate).format("YYYY-MM-DD");
      setDateRangeValue([moment(startDates), moment(endDates)]);
      getMeetingList(startDates, endDates);
    } else if (dataDuration === "Last 30 days") {
      const startDates = moment(todaysDate)
        .subtract(30, "days")
        .format("YYYY-MM-DD");
      const endDates = moment(todaysDate).format("YYYY-MM-DD");
      setDateRangeValue([moment(startDates), moment(endDates)]);
      getMeetingList(startDates, endDates);
    } else if (dataDuration === "All") {
      setDateRangeValue(null);
      getMeetingList();
    }
  }, [dataDuration]);

  useEffect(() => {
  
      getMeetingList("", "", searchValue);
    
  }, [searchValue]);

  const handleSelectAllRecordings = (checked) => {
    setSelectAllRecordings(checked);
    if (checked) {
      setSelectedItems(recordingData);
    } else {
      setSelectedItems([]);
    }
  };

  const handleDeleteRecording = async (meetingIds) => {
    setRecordingLoading(true);
    try {
      const res = await VideoConferenceService.deleteMeetingRecordingService(meetingIds);
      const { success, message } = res;
      if (success === 1) {
        modalNotification({
          type: "success",
          message: "Recording deleted successfully",
        });
        getMeetingList();
        setSelectedItems([]);
        setSelectAllRecordings(false);
      } else {
        modalNotification({
          type: "error",
          message,
        });
      }
    } catch (error) {
      logger(error);
      modalNotification({
        type: "error",
        message: "Failed to delete recording",
      });
    }
    setRecordingLoading(false);
  };

  const tabsDetails = (
    // <div
    //   className={`purchasePlan_wrap d-flex align-items-top ${
    //     subscriptionLoading ? "justify-content-center" : ""
    //   }`}
    // >
    //   {subscriptionLoading ? (
    //     <GlobalLoader />
    //   ) : (
    <>
      {/* {subcriptionData.length > 0 ? ( */}
      {defaultKey === "recorded" && (
        <div className="recorded-meetings-header">
          <div className="recorded-meetings-header-top d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-3">
            <div className="recorded-meetings-header-left d-flex flex-column flex-sm-row align-items-start align-items-sm-center gap-3">
              <div className="d-flex align-items-center gap-3">
                <h4 className="mb-0">Recorded Meetings</h4>
                <Dropdown
                  menu={{ items: dropdownItems }}
                  className="dropdown-duration"
                  openClassName="dropdown-duration-open"
                  overlayClassName="dropdown-duration-overlay"
                  trigger="click"
                >
                  <span>
                    {dataDuration} <FaSortDown />
                  </span>
                </Dropdown>
              </div>
              <div className="recorded-meetings-header-pages d-flex align-items-center">
                <div className="recorded-meetings-header-pages-count">
                  {count > 0
                    ? (parseInt(page) - 1) * parseInt(sizePerPage) + 1
                    : count}
                  -
                  {count > 0
                    ? page * sizePerPage <= count
                      ? page * sizePerPage
                      : count
                    : count}{" "}
                  of {count}
                </div>
                <div className="recorded-meetings-header-pages-arrows">
                  <Link
                    className="page-link"
                    to="#"
                    onClick={(e) => {
                      e.preventDefault();
                      if (page > 1) {
                        goToPage(page - 1);
                      }
                    }}
                  >
                    <MdKeyboardArrowLeft />
                  </Link>
                  <Link
                    onClick={(e) => {
                      e.preventDefault();
                      if (page < noOfPage) {
                        goToPage(page + 1);
                      }
                    }}
                    className="page-link"
                    to="#"
                  >
                    <MdKeyboardArrowRight />
                  </Link>
                </div>
              </div>
            </div>
            <Search
              placeholder="Search meetings"
              onSearch={(value) => {
                setSearchValue(value);
              }}
           
              style={{ maxWidth: "300px" }}
              className="search-meetings w-100 w-sm-auto"
            />
          </div>
          <div className="recorded-meetings-header-bottom d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center gap-3 mt-3">
            <div
              className={`recorded-meetings-header-bottom-left d-flex align-items-center gap-2 ${
                selectedItems.length > 0 &&
                "recorded-meetings-header-bottom-left-highlight"
              }`}
            >
              <Checkbox
                className="checkbox-meetings heading-checkbox"
                checked={selectAllRecordings}
                onChange={(e) => {
                  setSelectAllRecordings(e.target.checked);
                  handleSelectAllRecordings(e.target.checked);
                }}
              />
              <MdOutlineFileDownload
                className="action-icons"
                onClick={() => {
                  if (selectedItems.length === 0) {
                    modalNotification({
                      type: "error",
                      message:
                        "Please select atleast one recording to download",
                    });
                  } else {
                    downloadRecording();
                  }
                }}
              />
              <RiDeleteBin6Line
                className="action-icons action-icons-delete"
                onClick={() => {
                  if (selectedItems.length === 0) {
                    modalNotification({
                      type: "error",
                      message: "Please select atleast one recording to delete",
                    });
                  } else {
                    setShowDeleteRecordingModal(true);
                    foundMultipleRecordings();
                  }
                }}
              />
            </div>
            <RangePicker
              className="date-range w-50 w-sm-auto"
              value={dateRangeValue}
              format="DD MMM YYYY"
              onChange={handleDateRangeChange}
              disabledDate={(current) => {
                // Disable future dates
                return current && current > moment().endOf('day');
              }}
            />
          </div>
        </div>
      )}
      {tableLoading ? (
        <>
          <GlobalLoader />
        </>
      ) : tableData?.length > 0 ? (
        <>
          {" "}
          <div
            className={`meetingTable ${
              defaultKey === "recorded" && "recorded-meetings"
            }`}
          >
            <Table responsive className="mb-0 meetings-table">
              <thead>
                {defaultKey === "recorded" && (
                  <tr className="recording-table-headings">
                    <th aria-hidden="true" />
                    <th>Meeting Title</th>
                    <th>Meeting Date</th>
                    <th>Meeting Time</th>
                    <th>Recording Size / Duration</th>
                    <th>Status</th>
                    <th>View</th>
                  </tr>
                )}
              </thead>
              <tbody>
                {recordingData.map((item, index) => (
                  <tr
                    key={item?.id}
                    onClick={() => {
                      if (defaultKey === "recorded") {
                        const generatedUrl = baseUrlGenerator(
                          `${
                            userRoutesMap.SHOW_MEETING_RECORDINGS.path
                          }/${encoder(item?.id)}`
                        );
                        window.open(generatedUrl);
                      }
                    }}
                    style={{
                      cursor: defaultKey === "recorded" ? "pointer" : "default",
                    }}
                  >
                    {defaultKey === "recorded" ? (
                      <>
                        <td>
                          <Checkbox
                            className="checkbox-meetings meeting-checkbox"
                            checked={
                              selectAllRecordings ||
                              selectedItems.includes(item)
                            }
                            onClick={(e) => {
                              e.stopPropagation();
                            }}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedItems([...selectedItems, item]);
                              } else {
                                const newSelectedItems = selectedItems.filter(
                                  (selectedItem) => selectedItem !== item
                                );
                                setSelectedItems(newSelectedItems);
                                if (newSelectedItems.length === 0) {
                                  setSelectAllRecordings(false);
                                }
                              }
                            }}
                          />
                        </td>
                        <td className="recorded-meetings-title">
                          <RecordedMeeting />
                          <span
                            onClick={() => {
                              const generatedUrl = baseUrlGenerator(
                                `${
                                  userRoutesMap.SHOW_MEETING_RECORDINGS.path
                                }/${encoder(item?.id)}`
                              );
                              window.open(generatedUrl);
                            }}
                          >
                            {textFormatter(item?.event_name)}
                          </span>
                        </td>
                        <td>
                          {dateFormatter(item?.start_date, "DD MMM YYYY")}
                        </td>
                        <td>{dateFormatter(item?.start_date, "HH:mm")}</td>
                        <td>
                          {item?.total_file_size
                            ? item?.total_file_size
                            : "N/A"}
                          &nbsp;/&nbsp;
                          {item?.total_duration >= 60
                            ? `${(item?.total_duration / 60)?.toFixed(2)} hr`
                            : `${item?.total_duration} min`}
                        </td>
                        <td className={`recorded-meetings-${item.status}`}>
                          <span>{item?.status ? item?.status : "active"}</span>
                        </td>
                        <td
                        // onClick={() => {
                        //   // handleRecordingModalShow(item?.id);
                        // }}
                        // onClick={() => {
                        //   const generatedUrl = baseUrlGenerator(
                        //     `${
                        //       userRoutesMap.SHOW_MEETING_RECORDINGS.path
                        //     }/${encoder(item?.id)}`
                        //   );
                        //   window.open(generatedUrl);
                        // }}
                        >
                          <IoMdEye
                            style={{
                              cursor: "pointer",
                            }}
                          />
                        </td>
                      </>
                    ) : (
                      <>
                        <td>
                          {dateFormatter(item?.start_date, "DD MMM YYYY")}
                        </td>
                        <td>
                          {dateFormatter(item?.start_date, "HH:mm")} <br />{" "}
                          {dateFormatter(
                            item?.is_recurring
                              ? item?.MeetingTimes?.[
                                  item?.MeetingTimes?.length - 1
                                ]?.end_time
                              : item?.MeetingTimes?.[0]?.end_time,
                            "HH:mm"
                          )}
                        </td>
                        <td>
                          {textFormatter(item?.event_name)}
                          <br /> <span className="font-rg">Host - </span>{" "}
                          {checkValidData(item?.host)}
                        </td>
                        <td>
                          {item?.is_recurring && (
                            <>
                              Untill{" "}
                              {dateFormatter(
                                item?.MeetingTimes[
                                  item?.MeetingTimes?.length - 1
                                ].end_time,
                                "DD MMM YYYY, HH:mm"
                              )}
                              {/* {dateFormatter(
                                    item?.MeetingTimes?.[
                                      item?.MeetingTimes?.length - 1
                                    ]?.end_time,
                                    "DD MMM YYYY, HH:mm"
                                  )} */}
                            </>
                          )}
                        </td>
                        <td>
                          {defaultKey === "previous" && (
                            <>
                              {!loadingRows[item?.id] ? (
                                <div
                                  className="d-flex justify-content-center"
                                  style={{ width: "100%" }}
                                  key={index}
                                >
                                  <button
                                    className="btn btn-primary"
                                    onClick={() =>
                                      getAttendanceReport(item?.id)
                                    }
                                  >
                                    Attendance
                                  </button>
                                </div>
                              ) : (
                                <div className="d-flex justify-content-center">
                                  <Loader />
                                </div>
                              )}
                            </>
                          )}
                        </td>
                      </>
                    )}
                    {defaultKey === "upcoming" && item?.id && (
                      <td className="text-end">
                        <RippleEffect>
                          {/* Changes made due to livekit render */}
                          {/* <a
                                href={baseUrlGenerator(
                                  `${userRoutesMap.DAAKIA_VC_MEET.path}/${encoder(
                                    item?.room_uid
                                  )}`
                                )}
                                onClick={(event) => validateStartMeeting(event)}
                                target="_blank"
                                rel="noreferrer"
                                className="meetingsSec_meetingIcons user"
                                aria-label="video"
                              >
                                <em className="icon-video-outline" />
                              </a> */}
                          <a
                            onClick={(event) => {
                              event.preventDefault(); // Prevent default link behavior
                              const isValid = validateStartMeeting(event, item);

                              // Generate the URL and send it to the main process
                              if (isValid) {
                                const generatedUrl = baseUrlGenerator(
                                  `${
                                    userRoutesMap.DAAKIA_VC_MEET.path
                                  }/${encoder(item?.room_uid)}`
                                );
                                if (isElectron()) {
                                  // window.electronAPI.ipcRenderer.send(
                                  //   "load-meeting",
                                  //   generatedUrl
                                  // );
                                  loadMeetingInDesktopApp(generatedUrl);
                                } else {
                                  window.open(generatedUrl);
                                }
                              }
                            }}
                            className="meetingsSec_meetingIcons user"
                            aria-label="video"
                          >
                            <em className="icon-video-outline" />
                          </a>
                        </RippleEffect>
                        {/* 3 is for meeting completed */}   
                        {/* {![2, 3, 4].includes(item?.conference_status_id) && ( */}                        {![2, 4].includes(item?.conference_status_id) && (
                          <RippleEffect>
                            <Link
                              to="#"
                              className="meetingsSec_meetingIcons edit"
                              onClick={(e) => {
                                e.preventDefault();
                                getMeetingDetail(item);
                                handlePlanMeetingShow();
                              }}
                            >
                              <em className="icon-edit" />
                            </Link>
                          </RippleEffect>
                        )}
                        <RippleEffect>
                          <Link
                            to="#"
                            className="meetingsSec_meetingIcons delete"
                            onClick={(e) => {
                              e.preventDefault();
                              setIsAlertVisible(true);
                              setDeleteId(item?.id);
                            }}
                          >
                            <em className="icon-delete" />
                          </Link>
                        </RippleEffect>
                        <RippleEffect>
                          <Link
                            to="#"
                            className="meetingsSec_meetingIcons share"
                            onClick={(e) => {
                              e.preventDefault();
                              setShareId(item?.room_uid);
                              setShareMeetingInfo({
                                event_name: item?.event_name,
                                start_date: item?.start_date,
                                end_date: item?.end_max_date,
                              });
                              handleShareModalShow();
                              getMeetingSipData(item?.room_uid);
                            }}
                          >
                            <em className="icon-share" />
                          </Link>
                        </RippleEffect>
                      </td>
                    )}
                    {/* {defaultKey === "recorded" && (
                          <td className="text-end"> */}
                    {/* <RippleEffect>
                              <a
                                href={item?.recording_url}
                                className="meetingsSec_meetingIcons user"
                                target="_blank"
                                rel="noreferrer"
                                download
                              >
                                {" "}
                                <em className="icon-download" />
                              </a>
                            </RippleEffect> */}
                    {/* <RippleEffect>
                              <Link
                                to="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  setIsAlertRecordingVisible(true);
                                  setDeleteId(item?.id);
                                }}
                                className="meetingsSec_meetingIcons delete"
                              >
                                <em className="icon-delete" />
                              </Link>
                            </RippleEffect> */}
                    {/* <RippleEffect>
                              <Link
                                to="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  handleRecordingModalShow(item?.id);
                                }}
                                className="meetingsSec_meetingIcons share"
                              > */}
                    {/* <em className="icon-share" /> */}
                    {/* <InfoCircleFilled />
                              </Link>
                            </RippleEffect>
                          </td>
                        )} */}
                  </tr>
                ))}
              </tbody>
            </Table>{" "}
          </div>
          {count > 0 && (
            <div className="mt-3">
              <Pagination
                count={count}
                page={page}
                sizePerPage={sizePerPage}
                noOfPage={noOfPage}
                goToPage={goToPage}
              />
            </div>
          )}
        </>
      ) : (
        <div className="emptySec text-center w-100">
          <ImageElement
            source="video-conferencing-icon.svg"
            alt="No Data Found"
          />
          <h2>{t("text.common.noData")}</h2>
          <p className="mb-0">There are no meetings to show here right now.</p>
        </div>
      )}
    </>
    //   )}
    // </div>
  );
  const tabsContent = [
    {
      title: "Upcoming Meetings",
      key: "upcoming",
      content: tabsDetails,
    },
    {
      title: "Previous Meetings",
      key: "previous",
      content: tabsDetails,
    },
    {
      title: "Recorded Meetings",

      key: "recorded",
      content: tabsDetails,
    },
  ];

  const startMeetingsSubmit = async (value) => {
    // const userVideoConferenceSubscription = userActiveSubscriptions.find(
    //   (subscription) => subscription.plan_category === "videoConferencing"
    // );
    const region = await getRegion();
    setStartMeetingLoading(true);
    try {
      let bodyData = {
        start_date: dateFormatter(new Date(), "YYYY-MM-DD"),
        start_time: dateFormatter(new Date(), "HH:mm"),
        isStartNow: true,
        ...value,
      };

      let date = `${bodyData?.start_date} ${bodyData?.start_time}`;
      bodyData.start_date = momentTimezoneFormatter(new Date(date))
        .utc()
        .format();
      bodyData.seleted_time = date;
      delete bodyData.start_time;
      let activeAccount = getActiveAccount(userAccounts);
      bodyData.is_corporate = getIscorporateActive(userAccounts);
      bodyData.corporate_id = activeAccount?.id;
      bodyData.room_uid = "instant";
      bodyData.event_type = "Conference";
      bodyData.time_zone = "(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi";
      bodyData.timezone_identifier = "Asia/Kolkata";
      // bodyData.event_mode = userVideoConferenceSubscription?.Subscription?.SubscriptionFeature?.lobby ?  "lobby" : "follow";
      bodyData.event_mode = "follow";
      bodyData.is_recurring = 0;
      bodyData.preferred_video_server_id =
        region?.data?.preferred_video_server_id;

      let res = await VideoConferenceService.addMeetingService(bodyData);
      const { success, message, data } = res;
      if (success === 1) {
        modalNotification({
          type: "success",
          message,
        });
        if (data?.id) {
          // Changes made to open livekit in new tab
          const generatedUrl = baseUrlGenerator(
            `${userRoutesMap.DAAKIA_VC_MEET.path}/${encoder(data?.room_uid)}`
          );
          if (isElectron()) {
            // window.electronAPI.ipcRenderer.send("load-meeting", generatedUrl);
            loadMeetingInDesktopApp(generatedUrl);
          } else {
            window.open(generatedUrl);
          }
        }
        getMeetingList();
        setStartMeetingModal(false);
      } else {
        modalNotification({
          type: "error",
          message,
        });
      }

      // handleStartMeetingModalClose();
    } catch (error) {
      logger(error);
    }
    setStartMeetingLoading(false);
  };

  const handleTabChange = (value) => {
    setDefaultKey(value);
    setPage(1);
  };
  const handleRefresh = () => {
    setPage(1);
    setDataDuration("All");
    setDateRangeValue(null);
    setSearchValue("");
    getMeetingList();
  };

  const name = userData?.UserProfile?.full_name?.split(" ");

  const handleJoinMeetingModalShow = () => {
    setJoinMeetingModal(true);
  };

  const joinMeetingsSubmit = async (value) => {
    setJoinMeetingLoading(true);
    if (!value.meeting_url) {
      modalNotification({
        type: "error",
        message: "Meeting URL is required",
      });
      setJoinMeetingLoading(false);
      return;
    }
    const valid = validateMeetingUrl(value.meeting_url, true);
    if (valid && valid.valid) {
      loadMeetingInDesktopApp(value.meeting_url);
    } else {
      modalNotification({
        type: "error",
        message: valid.message,
      });
    }
    setJoinMeetingLoading(false);
  };

  const handleJoinMeetingModalClose = () => {
    setJoinMeetingModal(false);
  };

  return (
    <>
      {!!videoSubscription?.id && videoSubscription?.status === "active" ? (
        <div className="videoConferencing">
          <section className="userInfo">
            <Container>
              <Row className="align-items-center">
                <Col sm="6" className="mb-2 mb-sm-0">
                  <h1 className="font-bd">
                    {t("text.videoConferencing.hello")}{" "}
                    {textFormatter(name?.[0])} !
                    {/* {nameFormatter(
                      textFormatter(userData?.first_name),
                      textFormatter(userData?.last_name)
                    )} */}
                  </h1>
                  <h4 className="font-sb">
                    {t("text.videoConferencing.whatWould")}
                  </h4>
                  <div className="d-flex justify-content-center justify-content-sm-start align-items-center userInfo_links">
                    <a
                      // href={userRoutesMap.JITSI_MEET.path}
                      href="#"
                      className="text-center ripple-effect"
                      target="_blank"
                      rel="noreferrer"
                      onClick={(e) => {
                        e.preventDefault();
                        handleStartMeetingModalShow();
                      }}
                    >
                      <em className="icon-video" />
                      <p className="font-bd text-white mt-2 mt-md-3 mb-0">
                        {t("text.videoConferencing.starta")} <br />{" "}
                        {t("text.videoConferencing.meeting")}
                      </p>
                    </a>
                    <Link
                      to="#"
                      onClick={() => handlePlanMeetingShow()}
                      className="text-center ripple-effect"
                    >
                      <em className="icon-meeting" />
                      <p className="font-bd text-white mt-2 mt-md-3 mb-0">
                        {t("text.videoConferencing.planA")}
                        <br /> {t("text.videoConferencing.meeting")}
                      </p>
                    </Link>
                    {isElectron() && (
                      <a
                        href="#"
                        className="text-center ripple-effect"
                        style={{
                          backgroundColor: "#13294B",
                          width: "min-content",
                        }}
                        target="_blank"
                        rel="noreferrer"
                        onClick={(e) => {
                          e.preventDefault();
                          handleJoinMeetingModalShow();
                        }}
                      >
                        <em className="icon-video" />
                        <p className="font-bd text-white mt-2 mt-md-3 mb-0">
                          {/* {t("text.videoConferencing.joinMeeting")} */}
                          Join Meeting
                        </p>
                      </a>
                    )}
                  </div>
                </Col>
                <Col sm="6">
                  <ImageElement
                    source="video-conferencing.png"
                    alt="Video-conferencing"
                    className="img-fluid"
                  />
                </Col>
              </Row>
            </Container>
          </section>

          {/* metting */}

          <section className="meetingsSec">
            <Container>
              <div className="meetingsSec_wrap bg-white">
                <div className="meetingsSec_head d-lg-flex flex-wrap align-items-center justify-content-between">
                  <h4 className="font-bd mb-2 mb-lg-0">
                    {t("text.videoConferencing.meetings")}
                  </h4>
                  <ReloadOutlined
                    className="reloadIcon me-auto ms-2 ms-lg-4"
                    onClick={handleRefresh}
                    style={{ cursor: 'pointer' }}
                  />
                  <Tabs
                    tabContent={tabsContent}
                    activeKey={defaultKey}
                    setActiveKey={handleTabChange}
                    navClass="border-0"
                    tabsFor="video"
                  />
                </div>
              </div>
            </Container>
          </section>
        </div>
      ) : (
        <div className="dataFound d-flex justify-content-center align-items-center flex-column">
          <div className="emptySec text-center w-100 ">
            <ImageElement
              source="translation-services.svg"
              alt="No Data Found"
            />
            <h2>{t("text.common.noData")}</h2>
            {!isGuest && (
              <Link
                to={userRoutesMap.PURCHASE_PLAN.path}
                className="btn btn-primary btn-md"
              >
                {t("text.planAndSubscription.purchasePlan")}
              </Link>
            )}
            {/* <p className="mb-0">
                  There are no Pricing Plan to show here right now.
                </p> */}
          </div>
        </div>
      )}
      <ModalComponent
        show={planMeeting}
        modalExtraClass="noHeader planMeetingModal"
        onHandleVisible={handlePlanMeetingShow}
        onHandleCancel={handleClose}
        title=""
        size="lg"
      >
        <div className="modalHeader">
          {planData?.id ? (
            <h3>{t("text.videoConferencing.editMeeting")}</h3>
          ) : (
            <h3>{t("text.videoConferencing.planMeeting")}</h3>
          )}
        </div>
        <PlanMeetingForm
          onSubmit={onSubmit}
          loading={loading}
          handleClose={handleClose}
          planData={planData}
          timezoneData={timezoneData}
          userData={userData}
        />
      </ModalComponent>
      <SweetAlert
        title={t("text.common.areYouSure")}
        text={t("text.videoConferencing.delete")}
        show={isAlertVisible}
        icon="warning"
        showCancelButton
        confirmButtonText={t("text.common.yes")}
        cancelButtonText={t("text.common.no")}
        setIsAlertVisible={setIsAlertVisible}
        showLoaderOnConfirm
        loading={deleteLoading}
        onConfirmAlert={onConfirmAlert}
      />
      <SweetAlert
        title={t("text.common.areYouSure")}
        text="You want to delete this recording"
        show={isAlertRecordingVisible}
        icon="warning"
        showCancelButton
        confirmButtonText={t("text.common.yes")}
        cancelButtonText={t("text.common.no")}
        setIsAlertVisible={setIsAlertRecordingVisible}
        showLoaderOnConfirm
        loading={recordingLoading}
        onConfirmAlert={onConfirmRecordingAlert}
      />

      <ModalComponent
        show={shareModal}
        modalExtraClass="noHeader shareModal"
        onHandleVisible={() => {
          handleShareModalShow();
        }}
        onHandleCancel={handleShareModalClose}
        title=""
        size="md"
      >
        <ShareForm
          shareId={shareId}
          shareMeetingInfo={shareMeetingInfo}
          sipData={sipData}
        />
      </ModalComponent>

      <ModalComponent
        show={recordingModal}
        modalExtraClass="noHeader shareModal"
        onHandleVisible={handleRecordingModalShow}
        onHandleCancel={handleRecordingModalClose}
        title=""
        size="md"
      >
        {/* <RecordingForm recordingUrl={recordingUrl} allRecordingUrl={allRecordingUrl} isRecordingLoading={isRecordingLoading} /> */}
        {/* <RecordingList
          loader={isRecordingLoading}
          recordings={allRecordingUrl}
          meetingId={meetingId}
        /> */}
        <RecordingLists
          loader={isRecordingLoading}
          recordings={allRecordingUrl}
          meetingId={meetingId}
        />
      </ModalComponent>
      <ModalComponent
        show={startMeetingModal}
        modalExtraClass="noHeader"
        onHandleVisible={handleStartMeetingModalShow}
        onHandleCancel={handleStartMeetingModalClose}
        title=""
        size="md"
      >
        <StartMeetingsForm
          onSubmit={startMeetingsSubmit}
          handleStartMeetingModalClose={handleStartMeetingModalClose}
          startMeetingloading={startMeetingloading}
        />
      </ModalComponent>
      <ModalComponent
        show={joinMeetingModal}
        modalExtraClass="noHeader"
        onHandleVisible={handleJoinMeetingModalShow}
        onHandleCancel={handleJoinMeetingModalClose}
        title=""
        size="md"
      >
        <JoinMeetingForm
          onSubmit={joinMeetingsSubmit}
          handleJoinMeetingModalClose={handleJoinMeetingModalClose}
          joinMeetingloading={joinMeetingloading}
        />
      </ModalComponent>
      <ModalComponent
        show={showDeleteRecordingModal}
        modalExtraClass="noHeader"
        onHandleVisible={() => {
          setShowDeleteRecordingModal(true);
        }}
        onHandleCancel={() => {
          setShowDeleteRecordingModal(false);
        }}
      >
        <div className="modalHeader">
          {recordingIds.length > 1 ? (
            <h3>Delete Recordings</h3>
          ) : (
            <h3>Delete Recording</h3>
          )}
        </div>
        <div className="modalBody">
          {recordingIds.length > 1 ? (
            <p>Are you sure you want to delete these recordings?</p>
          ) : (
            <p>Are you sure you want to delete this recording?</p>
          )}
          <div className="d-flex justify-content-end gap-2">
            <Button
              onClick={() => {
                setShowDeleteRecordingModal(false);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setShowDeleteRecordingModal(false);
                handleDeleteRecording(recordingIds);
              }}
              type="danger"
            >
              Delete
            </Button>
          </div>
        </div>
      </ModalComponent>
    </>
  );
}

export default VideoConferencing;
