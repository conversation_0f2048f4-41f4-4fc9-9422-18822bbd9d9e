.mobile-settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  font-family: 'Inter', sans-serif;

  .mobile-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    width: 100%;
    height: 100%;
  }

  .mobile-modal-content {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .mobile-modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      border-bottom: 1px solid #e8e8e8;
      background: #ffffff;
      flex-shrink: 0;

      .mobile-modal-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }

      .mobile-close-button {
        width: 32px;
        height: 32px;
        border: none;
        background: #f5f5f5;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #666;
        font-size: 14px;

        &:hover {
          background: #e8e8e8;
        }

        &:active {
          background: #d9d9d9;
        }
      }
    }

    .mobile-tabs {
      display: flex;
      background: #f7f7f7;
      padding: 0;
      flex-shrink: 0;

      .mobile-tab {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 12px 8px;
        border: none;
        background: transparent;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        transition: all 0.2s ease;

        .tab-icon {
          width: 18px;
          height: 18px;
          margin-bottom: 4px;
          color: #666;
          fill: #666;
        }

        .tab-text {
          font-size: 14px;
          font-weight: 500;
          color: #666;
        }

        &.active {
          border-bottom: 2px solid #3B60E4;
          background: transparent;

          .tab-icon {
            color: #3B60E4;
            fill: #3B60E4;
          }

          .tab-text {
            color: #3B60E4;
            font-weight: 600;
          }
        }

        &:hover:not(.active) {
          background: rgba(0, 0, 0, 0.05);
        }
      }
    }

    .mobile-content {
      flex: 1;
      overflow-y: auto;
      padding: 0;
      background: #ffffff;

      // Custom scrollbar for mobile
      &::-webkit-scrollbar {
        width: 3px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.3);
      }

      .mobile-section-header {
        padding: 20px 20px 16px 20px;
        border-bottom: 1px solid #f0f0f0;

        h3 {
          font-size: 20px;
          font-weight: 600;
          color: #3B60E4;
          margin: 0 0 4px 0;
        }

        p {
          font-size: 14px;
          color: #666;
          margin: 0;
        }
      }

      .mobile-settings-section {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
          padding-bottom: 32px; // Extra padding at bottom
        }

        // Override existing component styles for mobile
        .grid-container {
          display: flex !important;
          flex-direction: column !important;
          gap: 16px !important;
          grid-template-columns: none !important;

          .grid-cell {
            width: 100% !important;
            padding: 0 !important;
            background: transparent !important;
            border: none !important;
            min-height: auto !important;

            &.center {
              justify-content: flex-start !important;
              text-align: left !important;
            }
          }
        }

        .setting-row {
          margin-bottom: 16px !important;
          padding: 0 !important;

          &.three-column {
            display: flex !important;
            flex-direction: column !important;
            gap: 12px !important;

            .setting-label-col,
            .setting-description-col,
            .setting-control-col {
              width: 100% !important;
              padding: 0 !important;
            }

            .setting-label {
              font-size: 16px !important;
              font-weight: 600 !important;
              color: #333 !important;
              margin-bottom: 8px !important;
            }

            .setting-item {
              margin-bottom: 12px !important;

              .setting-sublabel {
                font-size: 14px !important;
                color: #666 !important;
                margin-bottom: 8px !important;
                line-height: 1.4 !important;
              }
            }
          }

          // Toggle settings
          &:not(.three-column) {
            display: flex !important;
            flex-direction: column !important;
            gap: 8px !important;

            .setting-info {
              width: 100% !important;

              .setting-label {
                font-size: 16px !important;
                font-weight: 600 !important;
                color: #333 !important;
                margin-bottom: 4px !important;
              }

              .setting-sublabel {
                font-size: 14px !important;
                color: #666 !important;
                line-height: 1.4 !important;
                margin-bottom: 12px !important;
              }
            }

            .setting-control {
              width: 100% !important;
              display: flex !important;
              align-items: center !important;
              justify-content: space-between !important;

              .ant-switch {
                min-width: 44px !important;
                height: 24px !important;

                .ant-switch-handle {
                  width: 20px !important;
                  height: 20px !important;
                  top: 2px !important;
                }

                &.ant-switch-checked {
                  .ant-switch-handle {
                    left: calc(100% - 22px) !important;
                  }
                }
              }
            }
          }
        }

        // Device dropdowns
        .custom-device-select,
        .ant-select {
          width: 100% !important;
          max-width: 100% !important;
          margin-bottom: 12px !important;
        }

        // Test buttons
        .test-button {
          width: 100% !important;
          margin-top: 8px !important;
          padding: 12px !important;
          font-size: 14px !important;
        }

        // Audio level indicators
        .audio-level-blocks,
        .mic-level-indicator {
          width: 100% !important;
          max-width: 100% !important;
          margin: 8px 0 !important;
        }

        // Volume controls
        .volume-control {
          width: 100% !important;
          margin: 8px 0 !important;

          .ant-slider {
            margin: 0 !important;
          }
        }

        // Brightness controls
        .setting-control:has(.ant-slider) {
          width: 100% !important;
          display: flex !important;
          align-items: center !important;
          gap: 12px !important;

          .ant-slider {
            flex: 1 !important;
          }

          .brightness-value {
            font-size: 14px !important;
            font-weight: 600 !important;
            color: #333 !important;
            min-width: 40px !important;
            text-align: right !important;
          }
        }
      }
    }
  }
}

// Ensure mobile modal is always on top
.mobile-settings-modal {
  z-index: 9999 !important;
}
