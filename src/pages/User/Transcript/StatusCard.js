import React from "react";
import "./StatusCard.scss";

export default function StatusCard({ status }) {
  return (
    <div className={`status-card status-card-${status}`}>
      <h5>Meeting Analysis</h5>
      <div className="status-card-outer">
        <span className="status-card-title">
          {status === "completed"
            ? "Completed"
            : status === "transcription_failed"
            ? "Transcription Failed"
            : status === "analysis_failed"
            ? "Analysis Failed"
            : status === "queued"
            ? "Queued"
            : status === "transcription_inprogress"
            ? "Transcription In Progress"
            : status === "analysis_inprogress"
            ? "Analysis In Progress"
            : status === "unavailable"
            ? "Data Unavailable"
            : status === "INACTIVE"
            ? "Subscription Feature Not Available"
            : ""}
        </span>
        <span className="status-card-description">
          {status === "completed"
            ? "Your meeting has been transcribed and analyzed successfully."
            : status === "queued"
            ? "Kindly please wait while your recording is being queued for transcription."
            : status === "transcription_failed"
            ? "Sorry, the transcription process failed. Please raise a ticket and we will resolve it soon!"
            : status === "analysis_failed"
            ? "Sorry, the analysis process failed. Please raise a ticket and we will resolve it soon!"
            : status === "transcription_inprogress"
            ? "Your meeting is being transcribed. You will be able to view the transcript here very soon."
            : status === "analysis_inprogress"
            ? "Your meeting is being analyzed. You will be able to view the analysis here very soon."
            : status === "unavailable"
            ? "No transcription or analysis data is available for this meeting."
            : status === "INACTIVE"
            ? "Your plan is not active, please activate your plan to use this feature."
            : ""}
        </span>
      </div>
    </div>
  );
}
