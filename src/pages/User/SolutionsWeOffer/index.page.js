import React from "react";
import { <PERSON>, Container, <PERSON> } from "react-bootstrap";
import { Helmet } from "react-helmet-async";
import { <PERSON> } from "react-router-dom";
import { ImageElement } from "../../../components";
import "./SolutionsWeOffer.scss";
import CardSolution from "../../../components/Cards/Home/SolutionWeOffer/CardSolution";
import Best from "./Assets/Best Video Conferencing Software for Small Business 1.png";
// import Secure from "./Assets/Secure & Encrypted Chat App1 1.png";
import Virtual from "./Assets/virtual & Online Conference Platforms1 1.png";
import Secure from "./Assets/secure.png"


export default function SolutionsWeOffer() {
  return (
    <>
      <Helmet>
        <title>Secure & Encrypted Digital Collaboration Solutions for Businesses</title>
        <meta name="description" content="Explore Daakia’s industry-specific solutions for encrypted messaging, video conferencing, and team collaboration. Built for secure and efficient business communication." />
        {/* <meta name="keywords" content="React, Helmet, <PERSON><PERSON>, Meta Tags" /> */}
      </Helmet>
      <section id="weOffer" className="py-70 offserSec position-relative">
        <Container>
          <div className="text-center heading mx-auto">
            <h3 className="heading_sub font-ad">Solutions We Offer</h3>
            <h1 className="heading_main text-white">
            Empower Your Business with Secure & Scalable Communication Solutions
            </h1>
            <p className="mb-0 text-white">
              &#34;Daakia&#34; is a first of its kind super app in the field of
              social & business communication. Our platform provides single stop
              solution for end-to-end encrypted chat & call, audio/video
              conference, social media and translation services. Daakia is an all-in-one digital collaboration platform designed to enhance team communication and productivity.
            </p>
          </div>
          <div className="main-content">
            {/* <h1>
              Empower Your Business with Secure & Scalable Communication Solutions
            </h1> */}
            {/* <p>
              Daakia is an all-in-one digital collaboration platform designed to enhance team communication and productivity.
            </p> */}
            {/* <button style={{ display: 'block', margin: '0 auto', padding: '10px 20px', fontSize: '16px', backgroundColor: '#007BFF', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>
              Request a Demo
            </button> */}
            <h2>
              Key Features & Solutions
            </h2>
            <div className="main-content-card">
              <CardSolution
                title="Secure & Encrypted Chat App"
                des1="End-to-end encrypted chat messenger for secure team communication and supports real-time messaging with encryption."
                image={Secure}
                // des2="Supports real-time messaging with encryption."
                // keyword="Keyword: encrypted chat app, encrypted chat messenger, secure messaging app for business"
              />
              <CardSolution
                title="Best Video Conferencing Software for Small Business"
                des1="High-quality video meeting platforms with low latency, Screen sharing, file sharing, and meeting recording."
                image={Best}
                // des2="Screen sharing, file sharing, and meeting recording."
                // keyword="Keywords: best video conferencing software for small business, video conferencing software, online meeting platforms."
              />
              <CardSolution
                title="Virtual & Online Conference Platforms"
                des1="Host virtual conferences and webinars for global teams, seamless integration with business tools."
                image={Virtual}
                // des2="Seamless integration with business tools."
                // keyword=" Keywords: online conference platforms, virtual conference software."
              />
            </div>

            <h2 style={{ fontFamily: 'NunitoSans-ExtraBold', marginTop: '40px', color:'white' }}>
              Why Daakia Over Other Video Meeting Platforms?
            </h2>
            <table style={{ width: '100%', borderCollapse: 'collapse', marginBottom: '40px' ,color:"white"}}>
              <thead>
                <tr>
                  <th style={{ padding: '10px', border: '1px solid #ddd',color: 'white' }}>Feature</th>
                  <th style={{ padding: '10px', border: '1px solid #ddd',color:'white' }}>Daakia</th>
                  <th style={{ padding: '10px', border: '1px solid #ddd', color:'white'}}>Others</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style={{ padding: '10px', border: '1px solid #ddd' }}>End-to-End Encryption</td>
                  <td style={{ padding: '10px', border: '1px solid #ddd' }}>Yes</td>
                  <td style={{ padding: '10px', border: '1px solid #ddd' }}>No</td>
                </tr>
                <tr>
                  <td style={{ padding: '10px', border: '1px solid #ddd' }}>HD Video & Audio</td>
                  <td style={{ padding: '10px', border: '1px solid #ddd' }}>Yes</td>
                  <td style={{ padding: '10px', border: '1px solid #ddd' }}>Limited</td>
                </tr>
                <tr>
                  <td style={{ padding: '10px', border: '1px solid #ddd' }}>Cross-Platform Access</td>
                  <td style={{ padding: '10px', border: '1px solid #ddd' }}>Yes</td>
                  <td style={{ padding: '10px', border: '1px solid #ddd' }}>No</td>
                </tr>
                <tr>
                  <td style={{ padding: '10px', border: '1px solid #ddd' }}>Secure File Sharing</td>
                  <td style={{ padding: '10px', border: '1px solid #ddd' }}>Yes</td>
                  <td style={{ padding: '10px', border: '1px solid #ddd' }}>No</td>
                </tr>
              </tbody>
            </table>

            <h2 style={{ fontFamily: 'NunitoSans-ExtraBold', marginTop: '40px' }}>
               Get Started with Daakia
            </h2>
            <p style={{ textAlign: 'center', fontFamily: 'Inter', marginBottom: '20px' }}>
              Experience secure business communication today!
            </p>
            <Link to="/contact-us" style={{ display: 'block', margin: '0 auto', padding: '10px 20px', fontSize: '16px', backgroundColor: '#fff', border: 'none', borderRadius: '5px', cursor: 'pointer' , marginBottom:'20px', width:"fit-content"}}>
              📞 Contact Us
            </Link>
          </div>
          <div className="offserSec_bottom">
            <Row className="justify-content-center">
              <Col lg={10}>
                <div className="d-flex flex-nowrap offserSec_img">
                  <div className="imgLeft">
                    <picture>
                      <source
                        type="image/webp"
                        srcSet="assets/images/frontend/offer-img1.webp"
                        alt="offer-img1"
                      />
                      <ImageElement
                        source="offer-img1.png"
                        alt="offer-img1"
                        className="img-fluid"
                      />
                    </picture>
                  </div>
                  <div className="imgRight ms-sm-2 mt-2 mt-sm-0">
                    <picture>
                      <source
                        type="image/webp"
                        srcSet="assets/images/frontend/offer-img2.webp"
                        alt="offer-img2"
                      />
                      <ImageElement
                        source="offer-img2.png"
                        alt="offer-img2"
                        className="img-fluid"
                      />
                    </picture>
                  </div>
                </div>
              </Col>
            </Row>
          </div>
        </Container>
      </section>
    </>
  );
}
