import React, { useState, useEffect, useRef } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import { ReactComponent as MicIcon } from './Assets/mic.svg';
import { ReactComponent as VideoIcon } from './Assets/video.svg';
import './MobileSettingsModal.scss';

// Import existing components
import {
  MicrophoneSettings,
  SpeakerSettings,
  CameraSettings,
  AudioEnhancementSettings,
  VideoEnhancementSettings,
} from './components/settingsPrejoinModal';

export default function MobileSettingsModal({
  open,
  setOpen,
  // Audio props
  audioDeviceId,
  setAudioDeviceId,
  audioTrack,
  audioEnabled,
  setAudioEnabled,
  // Video props
  videoDeviceId,
  setVideoDeviceId,
  // Speaker props
  speakerDeviceId,
  setSpeakerDeviceId,
  // Settings props
  room,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  brightness = 100,
  onBrightnessChange,
  // Volume props
  outputVolume = 100,
  onOutputVolumeChange,
  // Auto video off props
  autoVideoOff = false,
  onAutoVideoOffChange,
  // Auto audio off props
  autoAudioOff = false,
  onAutoAudioOffChange,
  // Permission props
  permissions = { camera: false, microphone: false },
  // Virtual Background Modal props
  setIsVisualEffectsModalOpen,
}) {
  const [activeTab, setActiveTab] = useState('audio');
  const [audioDevices, setAudioDevices] = useState([]);
  const [videoDevices, setVideoDevices] = useState([]);
  const [speakerDevices, setSpeakerDevices] = useState([]);
  const [speakerAudioLevel, setSpeakerAudioLevel] = useState(0);
  const [currentAudioLevel, setCurrentAudioLevel] = useState(0);
  const gainNodeRef = useRef(null);

  // Device enumeration
  useEffect(() => {
    const enumerateDevices = async () => {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();

        const audioInputs = devices.filter(device => device.kind === 'audioinput');
        const audioOutputs = devices.filter(device => device.kind === 'audiooutput');
        const videoInputs = devices.filter(device => device.kind === 'videoinput');

        setAudioDevices(audioInputs);
        setSpeakerDevices(audioOutputs);
        setVideoDevices(videoInputs);
      } catch (error) {
        console.error('Error enumerating devices:', error);
      }
    };

    if (open) {
      enumerateDevices();
    }
  }, [open]);

  const handleClose = () => {
    setOpen(false);
  };

  const handleVideoDeviceChange = (deviceId) => {
    setVideoDeviceId(deviceId);
  };

  if (!open) return null;

  return (
    <div className="mobile-settings-modal">
      {/* Full screen overlay */}
      <div className="mobile-modal-overlay" onClick={handleClose} />

      {/* Modal content */}
      <div className="mobile-modal-content">
        {/* Header */}
        <div className="mobile-modal-header">
          <h2 className="mobile-modal-title">Settings</h2>
          <button className="mobile-close-button" onClick={handleClose}>
            <CloseOutlined />
          </button>
        </div>

        {/* Tabs */}
        <div className="mobile-tabs">
          <button
            className={`mobile-tab ${activeTab === 'audio' ? 'active' : ''}`}
            onClick={() => setActiveTab('audio')}
          >
            <MicIcon className="tab-icon" />
            <span className="tab-text">Audio</span>
          </button>
          <button
            className={`mobile-tab ${activeTab === 'video' ? 'active' : ''}`}
            onClick={() => setActiveTab('video')}
          >
            <VideoIcon className="tab-icon" />
            <span className="tab-text">Video</span>
          </button>
        </div>

        {/* Content */}
        <div className="mobile-content">
          {activeTab === 'audio' && (
            <div className="mobile-audio-content">
              <div className="mobile-section-header">
                <h3>Audio Settings</h3>
                <p>Change your audio settings here</p>
              </div>

              <div className="mobile-settings-section">
                <MicrophoneSettings
                  audioDevices={audioDevices}
                  audioDeviceId={audioDeviceId}
                  onAudioDeviceChange={setAudioDeviceId}
                  audioTrack={audioTrack}
                  audioEnabled={audioEnabled}
                  onAudioEnabledChange={setAudioEnabled}
                  currentAudioLevel={currentAudioLevel}
                  setCurrentAudioLevel={setCurrentAudioLevel}
                  permissions={permissions}
                />
              </div>

              <div className="mobile-settings-section">
                <SpeakerSettings
                  speakerDevices={speakerDevices}
                  speakerDeviceId={speakerDeviceId}
                  onSpeakerDeviceChange={setSpeakerDeviceId}
                  speakerAudioLevel={speakerAudioLevel}
                  setSpeakerAudioLevel={setSpeakerAudioLevel}
                  gainNodeRef={gainNodeRef}
                  outputVolume={outputVolume}
                  onOutputVolumeChange={onOutputVolumeChange}
                  permissions={permissions}
                />
              </div>

              <div className="mobile-settings-section">
                <AudioEnhancementSettings
                  autoAudioOff={autoAudioOff}
                  onAutoAudioOffChange={onAutoAudioOffChange}
                />
              </div>
            </div>
          )}

          {activeTab === 'video' && (
            <div className="mobile-video-content">
              <div className="mobile-section-header">
                <h3>Video Settings</h3>
                <p>Change your video settings here</p>
              </div>

              <div className="mobile-settings-section">
                <CameraSettings
                  videoDevices={videoDevices}
                  videoDeviceId={videoDeviceId}
                  onVideoDeviceChange={handleVideoDeviceChange}
                  permissions={permissions}
                />
              </div>

              <div className="mobile-settings-section">
                <VideoEnhancementSettings
                  isSelfVideoMirrored={isSelfVideoMirrored}
                  onMirrorVideoChange={setIsSelfVideoMirrored}
                  brightness={brightness}
                  onBrightnessChange={onBrightnessChange}
                  autoVideoOff={autoVideoOff}
                  onAutoVideoOffChange={onAutoVideoOffChange}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
