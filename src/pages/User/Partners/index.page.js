import { t } from "i18next";
import React, { useEffect, useState } from "react";
import { Container } from "react-bootstrap";
import Slider from "react-slick";
import { Helmet } from "react-helmet-async";
import { GlobalLoader, ImageElement } from "../../../components";
import { logger, modalNotification } from "../../../utils";
import { AdminManageClientPartner } from "../../../services";
import "./PartnerStyle.scss"
import PartnerCard from "../../../components/Cards/Home/Partners/PartnersCard";

function OurPartners() {
  let partnerSlider = {
    dots: true,
    arrow: true,
    rows: 2,
    infinite: false,
    speed: 2000,
    slidesToShow: 5,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 4000,
    responsive: [
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 4,
        },
      },
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 3,
        },
      },
      {
        breakpoint: 575,
        settings: {
          slidesToShow: 2,
          arrow: false,
        },
      },
    ],
  };

  const [clients, setClients] = useState([]);
  const [clientsLoader, setClientsloader] = useState(false);
  const [partners, setPartners] = useState([]);
  const [partnersLoader, setPartnersLoader] = useState(false);

  const getClientPartnerList = async (category) => {
    let setter;
    let loader;
    try {
      if (category === "Client") {
        setter = setClients;
        loader = setClientsloader;
      }
      else {
        setter = setPartners;
        loader = setPartnersLoader;
      }
      loader(true);
      let queryParams = {
        category,limit:"all"
      };
      const res = await AdminManageClientPartner.getClientPartnerListService({
        queryParams,
      });
      const { success, data, message } = res;
      if (success === 1) {
        setter(data?.rows);
      } else {
        modalNotification({
          type: "error",
          message,
        });
      }
    } catch (error) {
      logger(error);
    }
    loader(false);
  };


  useEffect(() => {
    getClientPartnerList("Client");
    getClientPartnerList("Partner");
  }, []);

  
  return (
    <>
      <Helmet>
        <title>Become a Daakia Partner | Secure SaaS Communication Solutions</title>
        <meta name="description" content="Learn how Daakia ensures secure communication through end-to-end encryption, private video conferencing, and cross-platform messaging for businesses and professionals." />
        {/* <meta name="keywords" content="React, Helmet, SEO, Meta Tags" /> */}
      </Helmet>
      <section className="ourPartnerSec py-70">
        <Container>
          <div className="text-center heading mx-auto">
            <h3 className="heading_sub font-ad">{t("text.partners.title")}</h3>
            <h1 className="heading_main">{t("text.partners.heading")}</h1>
          </div>
          <div className="partner">
            <h2>Our Valued Partners & Clients</h2>
            <ul>
              {/* <li>
                Display logos of notable clients and partners in a visually appealing format.
              </li>
              <li>
                Add a hover effect.
              </li> */}
            </ul>
          </div>
          <h2 style={{textAlign:"center"}}>Why Partner with Daakia?</h2>
          <div className="partner-content"> 
            <PartnerCard
              title="Smart & Secure Collaboration"
              description="AI-driven security guarantees secure and encrypted business correspondence"
            />
            <PartnerCard
              title="Smooth Integration"
              description="For a seamless workflow, connect with digital collaboration platforms with ease."
            />
            <PartnerCard
              title="Multilingual Communication"
              description="For smooth collaboration across borders, use AI-powered real-time translations to overcome language hurdles."
            />
            <PartnerCard
              title="Scalable and customizable"
              description="Solutions that are suited for startups, large corporations, and all points in between."
            />
            <PartnerCard
              title="Future-Ready Remote Work"
              description="Cloud-based productivity tools, HD video conferencing, and real-time document collaboration."
            />
          </div>
          <div className="partnerSlider mb-3 mb-lg-5 position-relative">
            <h2 className="font-30">{t("text.partners.ourClient")}</h2>
            <Slider className="innerSlider" {...partnerSlider}>
              {clientsLoader ? <GlobalLoader /> :
                clients?.length > 0 ?
                  clients?.map((item, index) => {
                    return (
                      <div key={index} className="partnerSlider_item d-flex justify-content-center align-items-center">
                        <ImageElement previewSource={item?.image_url} alt="hdfc-bank" className="img-fluid" />
                      </div>
                    );
                  }) 
                  :  <div className="emptySec text-center w-100">

                  {/* <ImageElement
    
                    source="video-conferencing-icon.svg"
    
                    alt="No Data Found"
    
                  /> */}
    
                  <h2>{t("text.common.noData")}</h2>
    
              
    
                </div>
              }
            </Slider>
            
          </div>
          <div className="partnerSlider position-relative">
            <h2 className="font-30">{t("text.partners.ourPartners")}</h2>
            <Slider className="innerSlider" {...partnerSlider}>
              {partnersLoader ? <GlobalLoader /> :
                partners?.length > 0 ?
                  partners?.map((item, index) => {
                    return (
                      <div key={index} className="partnerSlider_item d-flex justify-content-center align-items-center">
                        <ImageElement previewSource={item?.image_url} alt="hdfc-bank" className="img-fluid" />
                      </div>
                    );
                  }) : 
                  <div className="emptySec text-center w-100">

                    {/* <ImageElement

                      source="video-conferencing-icon.svg"

                      alt="No Data Found"

                    /> */}

                    <h2>{t("text.common.noData")}</h2>

                    {/* <p className="mb-0">

                      There are no Pricing Plan to show here right now.

                    </p> */}

                  </div>
              }
            </Slider>
          </div>
        </Container>
      </section>
    </>
  )
}

export default OurPartners;
